<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Unified coupon manager for stripepaymentpro plugin
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\manager;

use stdClass;
use moodle_database;
use moodle_url;
use Stripe\StripeClient;
use core\notification;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');
require_once($CFG->dirroot . '/enrol/stripepayment/lib.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/lib.php');

/**
 * Unified coupon manager class for handling all coupon operations
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class coupon_manager {

    /** @var StripeClient Stripe client for API communication */
    private StripeClient $stripe;

    /** @var object Plugin instance */
    private object $plugin;

    /** @var object Core plugin instance */
    private object $plugincore;

    /** @var moodle_database Database instance */
    private moodle_database $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $DB;
        
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepayment');
        $this->stripe = new StripeClient(get_config('enrol_stripepayment', 'secretkey'));
        $this->db = $DB;
    }

    /**
     * Get all coupons with course information for display
     * 
     * @return array Complete coupon data for template rendering
     */
    public function get_all_coupons_for_display(): array {
        // Fetch coupons
        $coupons = $this->db->get_records('enrol_stripepro_coupons');

        // Group coupons by course
        $coupons_by_course = [];
        $coupons_list = [];

        foreach ($coupons as $record) {
            $coupons_by_course[$record->stripe_product_id][] = $record->couponid;
            $coupons_list[$record->couponid] = $record;
        }

        // Fetch course names - Index by customtext2 (stripe_product_id)
        $courses = $this->get_courses_by_stripe_product_ids(array_keys($coupons_by_course));

        // Prepare template data
        $template_data = $this->prepare_template_data($coupons_by_course, $coupons_list, $courses);

        return [
            'coupons' => $template_data,
            'has_coupons' => !empty($template_data)
        ];
    }

    /**
     * Get courses by their Stripe product IDs with caching
     *
     * @param array $stripe_product_ids Array of Stripe product IDs
     * @return array Courses indexed by stripe_product_id
     */
    private function get_courses_by_stripe_product_ids(array $stripe_product_ids): array {
        static $course_cache = [];

        $courses = [];
        $uncached_ids = [];

        // Check cache first
        foreach ($stripe_product_ids as $id) {
            if (isset($course_cache[$id])) {
                $courses[$id] = $course_cache[$id];
            } else {
                $uncached_ids[] = $id;
            }
        }

        // Fetch uncached courses
        if (!empty($uncached_ids)) {
            list($insql, $inparams) = $this->db->get_in_or_equal($uncached_ids);
            $query = "SELECT e.customtext2, c.id, c.fullname, c.visible
                        FROM {course} c
                        INNER JOIN {enrol} e ON c.id = e.courseid
                       WHERE e.customtext2 $insql
                         AND e.enrol = 'stripepaymentpro'
                         AND e.status = 0
                       ORDER BY c.fullname";

            try {
                $course_records = $this->db->get_records_sql($query, $inparams);

                // Cache and index courses by stripe_product_id
                foreach ($course_records as $course) {
                    $course_cache[$course->customtext2] = $course;
                    $courses[$course->customtext2] = $course;
                }

                // Cache empty results to avoid repeated queries
                foreach ($uncached_ids as $id) {
                    if (!isset($course_cache[$id])) {
                        $course_cache[$id] = null;
                    }
                }
            } catch (\Exception $e) {
                debugging('Error fetching courses: ' . $e->getMessage(), DEBUG_DEVELOPER);
            }
        }

        // Add "All Courses" option for global coupons
        $courses['0'] = (object) [
            "customtext2" => '0',
            "id" => 0,
            "fullname" => "All Courses",
            "visible" => 1
        ];

        return $courses;
    }

    /**
     * Prepare coupon data for template rendering
     * 
     * @param array $coupons_by_course Coupons grouped by course
     * @param array $coupons_list All coupons data
     * @param array $courses Course data
     * @return array Template data
     */
    private function prepare_template_data(array $coupons_by_course, array $coupons_list, array $courses): array {
        $template_data = [];

        foreach ($coupons_by_course as $course_id => $coupons) {
            // Handle missing course data gracefully
            $course_name = isset($courses[$course_id]) ? 
                htmlspecialchars($courses[$course_id]->fullname) : 
                'Unknown Course (ID: ' . htmlspecialchars($course_id) . ')';
            
            $course_data = [
                'course_name' => $course_name,
                'course_id' => $course_id,
                'coupons' => [],
                'has_multiple_coupons' => count($coupons) > 1
            ];

            foreach ($coupons as $coupon_id) {
                $coupon = $coupons_list[$coupon_id];
                $course_data['coupons'][] = $this->format_coupon_for_display($coupon);
            }

            $template_data[] = $course_data;
        }

        return $template_data;
    }

    /**
     * Format a single coupon for display
     * 
     * @param stdClass $coupon Coupon record from database
     * @return array Formatted coupon data
     */
    private function format_coupon_for_display(stdClass $coupon): array {
        $discount_text = '';
        
        if ($coupon->percent_off > 0) {
            $discount_text = $coupon->percent_off . '% off';
        } else if (isset($coupon->amount_off) && $coupon->amount_off > 0) {
            $currency_symbol = html_entity_decode($this->plugincore->show_currency_symbol(strtolower($coupon->currency)));
            $amount = $coupon->amount_off / $this->plugin->get_fractional_unit_amount(strtoupper($coupon->currency));
            $discount_text = $currency_symbol . $amount . ' off';
        }

        $duration_text = ($coupon->duration == "repeating") ? 
            " for " . $coupon->no_of_months . " months" : " " . $coupon->duration;

        $expiry_text = $coupon->coupon_expiry > 0 ? 
            ". Expiry: " . userdate($coupon->coupon_expiry) : ". Expiry: Never";

        return [
            'coupon_id' => $coupon->couponid,
            'coupon_name' => htmlspecialchars($coupon->coupon_name),
            'discount_text' => $discount_text,
            'duration_text' => $duration_text,
            'expiry_text' => $expiry_text,
            'full_description' => $discount_text . $duration_text . $expiry_text
        ];
    }

    /**
     * Get courses available for Stripe enrollment with caching
     *
     * @return array Course list for form options
     */
    public function get_available_courses(): array {
        static $cached_courses = null;

        if ($cached_courses === null) {
            try {
                $courses_with_stripe = $this->db->get_records_sql(
                    "SELECT DISTINCT e.customtext2, c.fullname, c.visible
                            FROM {enrol} e
                            INNER JOIN {course} c ON e.courseid = c.id
                           WHERE e.enrol = 'stripepaymentpro'
                             AND e.status = 0
                             AND c.visible = 1
                           ORDER BY c.fullname"
                );

                $cached_courses = [];
                foreach ($courses_with_stripe as $course) {
                    if (!empty($course->customtext2)) {
                        $cached_courses[$course->customtext2] = $course->fullname;
                    }
                }

                // Add "All Courses" option
                $cached_courses = ['0' => get_string('all_courses', 'enrol_stripepaymentpro')] + $cached_courses;

            } catch (\Exception $e) {
                debugging('Error fetching available courses: ' . $e->getMessage(), DEBUG_DEVELOPER);
                $cached_courses = ['0' => get_string('all_courses', 'enrol_stripepaymentpro')];
            }
        }

        return $cached_courses;
    }

    /**
     * Create a new coupon in Stripe and save to database
     *
     * @param stdClass $formdata Form data from coupon creation form
     * @return bool Success status
     */
    public function create_coupon(stdClass $formdata): bool {
        // Validate form data
        if (!$this->validate_coupon_data($formdata)) {
            return false;
        }

        // Check for duplicate coupon name
        if ($this->db->record_exists('enrol_stripepro_coupons', ['coupon_name' => $formdata->coupon_name])) {
            notification::error(get_string('coupon_name_exists', 'enrol_stripepaymentpro'));
            return false;
        }

        $stripe_params = $this->build_stripe_coupon_params($formdata);

        try {
            $coupon = $this->stripe->coupons->create($stripe_params);

            if ($coupon && $coupon->id) {
                return $this->save_coupon_to_database($coupon, $formdata);
            } else {
                notification::error(get_string('coupon_creation_failed', 'enrol_stripepaymentpro'));
                return false;
            }
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            notification::error(get_string('stripe_invalid_request', 'enrol_stripepaymentpro') . ': ' . $e->getMessage());
            return false;
        } catch (\Stripe\Exception\AuthenticationException $e) {
            notification::error(get_string('stripe_auth_error', 'enrol_stripepaymentpro'));
            debugging('Stripe authentication error: ' . $e->getMessage(), DEBUG_DEVELOPER);
            return false;
        } catch (\Exception $e) {
            notification::error(get_string('coupon_creation_error', 'enrol_stripepaymentpro'));
            debugging('Coupon creation error: ' . $e->getMessage(), DEBUG_DEVELOPER);
            return false;
        }
    }

    /**
     * Validate coupon form data
     *
     * @param stdClass $formdata Form data to validate
     * @return bool True if valid, false otherwise
     */
    private function validate_coupon_data(stdClass $formdata): bool {
        // Check required fields
        if (empty($formdata->coupon_name)) {
            notification::error(get_string('coupon_name_required', 'enrol_stripepaymentpro'));
            return false;
        }

        if (empty($formdata->coupon_types)) {
            notification::error(get_string('coupon_type_required', 'enrol_stripepaymentpro'));
            return false;
        }

        if (empty($formdata->discount_amount) || $formdata->discount_amount <= 0) {
            notification::error(get_string('discount_amount_required', 'enrol_stripepaymentpro'));
            return false;
        }

        // Validate percentage discount
        if ($formdata->coupon_types === 'percent_off' && $formdata->discount_amount > 100) {
            notification::error(get_string('invalid_percentage', 'enrol_stripepaymentpro'));
            return false;
        }

        // Validate currency for amount-based discounts
        if ($formdata->coupon_types === 'amount_off' && empty($formdata->coupon_currency)) {
            notification::error(get_string('currency_required', 'enrol_stripepaymentpro'));
            return false;
        }

        return true;
    }

    /**
     * Build Stripe API parameters for coupon creation
     * 
     * @param stdClass $formdata Form data
     * @return array Stripe API parameters
     */
    private function build_stripe_coupon_params(stdClass $formdata): array {
        $params = ['name' => $formdata->coupon_name];
        
        if ($formdata->coupon_expiry > time()) {
            $params['redeem_by'] = $formdata->coupon_expiry;
        }

        if ($formdata->coupon_types == "amount_off") {
            $params['amount_off'] = intval($formdata->discount_amount) * 
                $this->plugin->get_fractional_unit_amount($formdata->coupon_currency);
            $params['currency'] = $formdata->coupon_currency;
        } else if ($formdata->coupon_types == "percent_off") {
            $params['percent_off'] = $formdata->discount_amount;
        }

        $params['duration'] = $formdata->coupon_duration;

        if ($formdata->coupon_duration == "repeating") {
            $params['duration_in_months'] = $formdata->coupon_duration_multiple_months_val;
        }

        if ($formdata->coupon_course_assignment != 0) {
            $params['applies_to']['products'] = [$formdata->coupon_course_assignment];
        }

        return $params;
    }

    /**
     * Save coupon data to database
     * 
     * @param object $coupon Stripe coupon object
     * @param stdClass $formdata Original form data
     * @return bool Success status
     */
    private function save_coupon_to_database(object $coupon, stdClass $formdata): bool {
        $record = new stdClass();
        $record->couponid = $coupon->id;
        $record->coupon_name = $coupon->name;
        $record->amount_off = $coupon->amount_off;
        $record->percent_off = $coupon->percent_off;
        $record->currency = $coupon->currency;
        $record->duration = $coupon->duration;
        $record->no_of_months = $coupon->duration_in_months ? $coupon->duration_in_months : 0;
        $record->stripe_product_id = $formdata->coupon_course_assignment;
        $record->timecreated = $coupon->created;
        $record->coupon_expiry = $coupon->redeem_by ? $coupon->redeem_by : 0;

        if (!$this->db->record_exists('enrol_stripepro_coupons', ['couponid' => $record->couponid])) {
            $this->db->insert_record('enrol_stripepro_coupons', $record);
            return true;
        } else {
            notification::error(get_string('duplicatedata', 'enrol_stripepaymentpro'));
            return false;
        }
    }

    /**
     * Delete a coupon from both Stripe and database
     * 
     * @param string $coupon_id Stripe coupon ID
     * @return bool Success status
     */
    public function delete_coupon(string $coupon_id): bool {
        try {
            // Delete from Stripe
            $this->stripe->coupons->delete($coupon_id);
            
            // Delete from database
            $this->db->delete_records('enrol_stripepro_coupons', ['couponid' => $coupon_id]);
            
            return true;
        } catch (\Exception $e) {
            notification::error($e->getMessage());
            return false;
        }
    }

    /**
     * Delete all coupons for a specific course
     * 
     * @param string $course_id Stripe product ID
     * @return bool Success status
     */
    public function delete_all_course_coupons(string $course_id): bool {
        $coupons = $this->db->get_records('enrol_stripepro_coupons', ['stripe_product_id' => $course_id]);
        
        $success = true;
        foreach ($coupons as $coupon) {
            if (!$this->delete_coupon($coupon->couponid)) {
                $success = false;
            }
        }
        
        return $success;
    }
}

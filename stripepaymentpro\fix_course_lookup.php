<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Fix course lookup issues for Stripe coupons
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/controller/coupon_controller.php');

use enrol_stripepaymentpro\controller\coupon_controller;

// Require admin login
require_login();
require_capability('moodle/site:config', context_system::instance());

$PAGE->set_url('/enrol/stripepaymentpro/fix_course_lookup.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Fix Course Lookup Issues');
$PAGE->set_heading('Stripe Payment Pro - Fix Course Lookup Issues');

$action = optional_param('action', '', PARAM_ALPHA);
$product_id = optional_param('product_id', '', PARAM_RAW);
$course_id = optional_param('course_id', 0, PARAM_INT);

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>Fix Course Lookup Issues</h2>';

// Test the enhanced coupon controller
echo '<h3>Testing Enhanced Course Lookup</h3>';
try {
    $coupon_controller = new coupon_controller();
    $coupon_data = $coupon_controller->get_coupons_data();
    
    echo '<div class="alert alert-success">✓ Enhanced Coupon Controller loaded successfully</div>';
    
    if ($coupon_data['has_coupons']) {
        echo '<h4>Current Coupon-Course Mappings:</h4>';
        echo '<div class="table-responsive">';
        echo '<table class="table table-striped">';
        echo '<thead><tr><th>Product ID</th><th>Course Name</th><th>Coupons</th><th>Action</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($coupon_data['coupons_by_course'] as $pid => $coupon_ids) {
            $course_name = isset($coupon_data['courses'][$pid]) ? 
                $coupon_data['courses'][$pid]->fullname : 'NOT FOUND';
            
            $row_class = (strpos($course_name, 'Course (') === 0) ? 'table-warning' : '';
            
            echo '<tr class="' . $row_class . '">';
            echo '<td><code>' . htmlspecialchars($pid) . '</code></td>';
            echo '<td>' . $course_name . '</td>';
            echo '<td>' . count($coupon_ids) . ' coupon(s)</td>';
            echo '<td>';
            if (strpos($course_name, 'Course (') === 0) {
                echo '<a href="?action=fix&product_id=' . urlencode($pid) . '" class="btn btn-sm btn-warning">Fix</a>';
            } else {
                echo '<span class="text-success">✓ OK</span>';
            }
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
        echo '</div>';
    } else {
        echo '<div class="alert alert-info">No coupons found.</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

// Handle fix action
if ($action === 'fix' && !empty($product_id)) {
    echo '<h3>Fix Product ID: ' . htmlspecialchars($product_id) . '</h3>';
    
    // Show available courses
    $courses = $DB->get_records_sql(
        "SELECT c.id, c.fullname, c.shortname, e.enrol, e.customtext2, e.status
         FROM {course} c
         LEFT JOIN {enrol} e ON c.id = e.courseid AND (e.enrol = 'stripepaymentpro' OR e.enrol = 'stripepayment')
         WHERE c.id > 1
         ORDER BY c.fullname"
    );
    
    echo '<h4>Available Courses:</h4>';
    echo '<form method="post" action="?action=update">';
    echo '<input type="hidden" name="product_id" value="' . htmlspecialchars($product_id) . '">';
    echo '<div class="table-responsive">';
    echo '<table class="table table-sm">';
    echo '<thead><tr><th>Select</th><th>Course</th><th>Current Product ID</th><th>Enrollment Status</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($courses as $course) {
        $current_product = $course->customtext2 ?: 'None';
        $status = $course->status == 0 ? 'Active' : 'Disabled';
        
        echo '<tr>';
        echo '<td><input type="radio" name="course_id" value="' . $course->id . '"></td>';
        echo '<td>' . htmlspecialchars($course->fullname) . ' <small>(' . htmlspecialchars($course->shortname) . ')</small></td>';
        echo '<td><code>' . htmlspecialchars($current_product) . '</code></td>';
        echo '<td>' . $status . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    echo '</div>';
    echo '<button type="submit" class="btn btn-primary">Assign Product ID to Selected Course</button>';
    echo '</form>';
    
    echo '<hr>';
    echo '<h4>Alternative Actions:</h4>';
    echo '<div class="row">';
    echo '<div class="col-md-6">';
    echo '<div class="card">';
    echo '<div class="card-body">';
    echo '<h5>Make Coupon Global</h5>';
    echo '<p>Change the product ID to "0" to make this coupon applicable to all courses.</p>';
    echo '<a href="?action=make_global&product_id=' . urlencode($product_id) . '" class="btn btn-info">Make Global</a>';
    echo '</div></div></div>';
    
    echo '<div class="col-md-6">';
    echo '<div class="card">';
    echo '<div class="card-body">';
    echo '<h5>Delete Orphaned Coupons</h5>';
    echo '<p>Remove all coupons with this product ID from both Stripe and Moodle.</p>';
    echo '<a href="?action=delete&product_id=' . urlencode($product_id) . '" class="btn btn-danger" onclick="return confirm(\'Are you sure?\')">Delete Coupons</a>';
    echo '</div></div></div>';
    echo '</div>';
}

// Handle update action
if ($action === 'update' && !empty($product_id) && !empty($course_id)) {
    echo '<h3>Updating Course Assignment</h3>';
    
    try {
        // Update the enrollment instance
        $enrollment = $DB->get_record('enrol', ['courseid' => $course_id, 'enrol' => 'stripepaymentpro']);
        
        if (!$enrollment) {
            // Create new enrollment instance
            $enrollment = new stdClass();
            $enrollment->enrol = 'stripepaymentpro';
            $enrollment->courseid = $course_id;
            $enrollment->status = 0;
            $enrollment->customtext2 = $product_id;
            $enrollment->timecreated = time();
            $enrollment->timemodified = time();
            
            $DB->insert_record('enrol', $enrollment);
            echo '<div class="alert alert-success">✓ Created new Stripe enrollment for course and assigned Product ID</div>';
        } else {
            // Update existing enrollment
            $enrollment->customtext2 = $product_id;
            $enrollment->timemodified = time();
            
            $DB->update_record('enrol', $enrollment);
            echo '<div class="alert alert-success">✓ Updated existing enrollment with new Product ID</div>';
        }
        
        echo '<a href="coupon_management.php" class="btn btn-primary">View Updated Coupon Management</a>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">✗ Error updating course: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

// Handle make global action
if ($action === 'make_global' && !empty($product_id)) {
    echo '<h3>Making Coupons Global</h3>';
    
    try {
        $updated = $DB->execute(
            "UPDATE {enrol_stripepro_coupons} SET stripe_product_id = '0' WHERE stripe_product_id = ?",
            [$product_id]
        );
        
        if ($updated) {
            echo '<div class="alert alert-success">✓ Successfully made all coupons with Product ID "' . htmlspecialchars($product_id) . '" global</div>';
            echo '<a href="coupon_management.php" class="btn btn-primary">View Updated Coupon Management</a>';
        } else {
            echo '<div class="alert alert-warning">No coupons were updated</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">✗ Error making coupons global: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

// Handle delete action
if ($action === 'delete' && !empty($product_id)) {
    echo '<h3>Deleting Orphaned Coupons</h3>';
    
    try {
        $coupons = $DB->get_records('enrol_stripepro_coupons', ['stripe_product_id' => $product_id]);
        $count = 0;
        
        foreach ($coupons as $coupon) {
            // Delete from database
            $DB->delete_records('enrol_stripepro_coupons', ['id' => $coupon->id]);
            $count++;
        }
        
        echo '<div class="alert alert-success">✓ Deleted ' . $count . ' orphaned coupon(s)</div>';
        echo '<a href="coupon_management.php" class="btn btn-primary">View Updated Coupon Management</a>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">✗ Error deleting coupons: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

echo '<div class="mt-3">';
echo '<a href="coupon_management.php" class="btn btn-secondary">Back to Coupon Management</a>';
echo '</div>';

echo '</div>';

echo $OUTPUT->footer();

{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template enrol_stripepaymentpro/coupon_management

    Template for coupon management interface

    Context variables required for this template:
    * coupons - Array of coupon data grouped by course
    * has_coupons - Boolean indicating if there are any coupons

    Example context (json):
    {
        "coupons": [
            {
                "course_name": "Course Name",
                "course_id": "prod_123",
                "has_multiple_coupons": true,
                "coupons": [
                    {
                        "coupon_id": "coupon_123",
                        "coupon_name": "DISCOUNT10",
                        "discount_text": "10% off",
                        "duration_text": " forever",
                        "expiry_text": ". Expiry: Never",
                        "full_description": "10% off forever. Expiry: Never"
                    }
                ]
            }
        ],
        "has_coupons": true
    }
}}

<!-- Navigation tabs -->
<nav class='strip-coupon-navigation-section' style='margin-bottom: 20px;'>
    <button id="all_coupon_button" style='margin-right: 10px;'>{{#str}}all_coupons, enrol_stripepaymentpro{{/str}}</button>
    <button id="generate_coupons_button">{{#str}}generate_coupons, enrol_stripepaymentpro{{/str}}</button>
</nav>

<!-- Display table of courses and associated coupons -->
<section class='all-coupons-section' id='all_coupons_section' style='display: block;'>
    {{#has_coupons}}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th scope="col">
                        <i class="fa fa-graduation-cap mr-2"></i>
                        {{#str}}course_name, enrol_stripepaymentpro{{/str}}
                    </th>
                    <th scope="col">
                        <i class="fa fa-tags mr-2"></i>
                        {{#str}}coupons, enrol_stripepaymentpro{{/str}}
                    </th>
                    <th scope="col">
                        <i class="fa fa-code mr-2"></i>
                        {{#str}}coupon_id, enrol_stripepaymentpro{{/str}}
                    </th>
                    <th scope="col">
                        <i class="fa fa-cogs mr-2"></i>
                        {{#str}}actions, enrol_stripepaymentpro{{/str}}
                    </th>
                </tr>
            </thead>
            <tbody>
                {{#coupons}}
                <tr>
                    <td>
                        <div class="course-name-cell">
                            <strong>{{course_name}}</strong>
                        </div>
                    </td>
                    <td class='coupon-list'>
                        {{#coupons}}
                        <div class='coupon-name'>
                            <div class="coupon-content">
                                <strong>{{coupon_name}}</strong>
                                {{#full_description}}
                                <small class="text-muted d-block">{{full_description}}</small>
                                {{/full_description}}
                            </div>
                            <button class="deactivate-coupon"
                                    data-courseid="{{course_id}}"
                                    data-couponid="{{coupon_id}}"
                                    title="{{#str}}delete_coupon, enrol_stripepaymentpro{{/str}}"
                                    aria-label="{{#str}}delete_coupon, enrol_stripepaymentpro{{/str}}">
                                <i class="fa fa-trash" aria-hidden="true"></i>
                            </button>
                        </div>
                        {{/coupons}}
                    </td>
                    <td class='coupon-id-list'>
                        {{#coupons}}
                        <div title="{{coupon_id}}">{{coupon_id}}</div>
                        {{/coupons}}
                    </td>
                    <td>
                        {{#has_multiple_coupons}}
                        <button class="deactivate-all-coupons"
                                data-courseid="{{course_id}}"
                                title="{{#str}}deactivate_all_coupons, enrol_stripepaymentpro{{/str}}"
                                aria-label="{{#str}}deactivate_all_coupons, enrol_stripepaymentpro{{/str}}">
                            <span>{{#str}}deactivate_all_coupons, enrol_stripepaymentpro{{/str}}</span>
                            <i class="fa fa-trash-alt" aria-hidden="true"></i>
                        </button>
                        {{/has_multiple_coupons}}
                        {{^has_multiple_coupons}}
                        <span class="text-muted">—</span>
                        {{/has_multiple_coupons}}
                    </td>
                </tr>
                {{/coupons}}
            </tbody>
        </table>
    </div>
    {{/has_coupons}}
    {{^has_coupons}}
    <div class="alert alert-info d-flex align-items-center">
        <i class="fa fa-info-circle mr-3" style="font-size: 1.5rem;"></i>
        <div>
            <strong>{{#str}}no_coupon_found, enrol_stripepaymentpro{{/str}}</strong>
            <p class="mb-0 mt-1">{{#str}}no_coupon_description, enrol_stripepaymentpro{{/str}}</p>
        </div>
    </div>
    {{/has_coupons}}
</section>

<!-- Form section for generating coupons -->
<section class='generate-coupon-section' id='generate_coupon_section' style='display: none;'>
    <h3>{{#str}}create_new_coupon, enrol_stripepaymentpro{{/str}}</h3>
    <div id="coupon-form-container">
        <!-- Form will be rendered by PHP and inserted here -->
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const allCouponsButton = document.getElementById('all_coupon_button');
    const generateCouponsButton = document.getElementById('generate_coupons_button');
    const allCouponsSection = document.getElementById('all_coupons_section');
    const generateCouponsSection = document.getElementById('generate_coupon_section');

    // Show all coupons section by default
    function showAllCoupons() {
        allCouponsSection.style.display = 'block';
        generateCouponsSection.style.display = 'none';
        allCouponsButton.classList.remove('btn-secondary');
        allCouponsButton.classList.add('btn-primary', 'active');
        generateCouponsButton.classList.remove('btn-primary', 'active');
        generateCouponsButton.classList.add('btn-secondary');
    }

    // Show generate coupons section
    function showGenerateCoupons() {
        allCouponsSection.style.display = 'none';
        generateCouponsSection.style.display = 'block';
        generateCouponsButton.classList.remove('btn-secondary');
        generateCouponsButton.classList.add('btn-primary', 'active');
        allCouponsButton.classList.remove('btn-primary', 'active');
        allCouponsButton.classList.add('btn-secondary');
    }

    // Event listeners
    if (allCouponsButton) {
        allCouponsButton.addEventListener('click', showAllCoupons);
    }

    if (generateCouponsButton) {
        generateCouponsButton.addEventListener('click', showGenerateCoupons);
    }

    // Initialize with all coupons view
    showAllCoupons();
});
</script>

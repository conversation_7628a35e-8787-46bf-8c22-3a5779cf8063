{"name": "stylelint", "version": "15.11.0", "description": "A mighty CSS linter that helps you avoid errors and enforce conventions.", "keywords": ["css-in-js", "css", "less", "lint", "linter", "markdown", "sass", "scss", "stylelint", "sugarss"], "homepage": "https://stylelint.io", "repository": "stylelint/stylelint", "funding": {"type": "opencollective", "url": "https://opencollective.com/stylelint"}, "license": "MIT", "author": "stylelint", "main": "lib/index.js", "types": "types/stylelint/index.d.ts", "bin": {"stylelint": "bin/stylelint.mjs"}, "files": ["bin/**/*.js", "bin/**/*.mjs", "lib/**/*.js", "lib/**/*.mjs", "!**/__tests__/**", "!lib/testUtils/**", "types/stylelint/index.d.ts"], "scripts": {"benchmark-rule": "node scripts/benchmark-rule.mjs", "format": "prettier . --write --cache", "lint": "npm-run-all --parallel --continue-on-error lint:*", "lint:formatting": "prettier . --check --cache", "lint:js": "eslint . --cache --max-warnings=0 --ext .js,.mjs", "lint:md": "remark . --quiet --frail", "lint:types": "tsc", "prepare": "husky install && patch-package", "release": "np --no-release-draft", "pretest": "npm run lint", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test-coverage": "npm test --ignore-scripts -- --coverage", "test-only": "npm test --ignore-scripts", "version": "changeset version", "postversion": "git restore package.json", "watch": "npm test --ignore-scripts -- --watch"}, "lint-staged": {"*.{js,mjs}": "eslint --cache --fix", "*.{js,json,md,mjs,ts,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint", "stylelint/jest"], "rules": {"n/prefer-global/process": "off"}, "overrides": [{"files": ["**/*.mjs"], "plugins": ["eslint-plugin-import"], "rules": {"import/extensions": ["error", "ignorePackages"]}}], "globals": {"__dirname": true, "module": true, "require": true, "testRule": true, "testRuleConfigs": true}, "root": true}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "jest": {"clearMocks": true, "collectCoverage": false, "collectCoverageFrom": ["lib/**/*.{js,mjs}", "!lib/**/{__tests__,testUtils}/**/*.{js,mjs}"], "coverageDirectory": "./.coverage/", "coverageProvider": "v8", "coverageReporters": ["lcov", "text-summary"], "coverageThreshold": {"global": {"branches": 75, "functions": 75, "lines": 75, "statements": 75}}, "moduleNameMapper": {"^stylelint$": "<rootDir>/lib/index.js"}, "preset": "jest-preset-stylelint", "roots": ["lib", "system-tests"], "testEnvironment": "node", "testRegex": ".*\\.test\\.m?js$|rules/.*/__tests__/.*\\.m?js$", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "dependencies": {"@csstools/css-parser-algorithms": "^2.3.1", "@csstools/css-tokenizer": "^2.2.0", "@csstools/media-query-list-parser": "^2.1.4", "@csstools/selector-specificity": "^3.0.0", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^8.2.0", "css-functions-list": "^3.2.1", "css-tree": "^2.3.1", "debug": "^4.3.4", "fast-glob": "^3.3.1", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^7.0.0", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.3.1", "ignore": "^5.2.4", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.29.0", "mathml-tag-names": "^2.1.3", "meow": "^10.1.5", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.28", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.13", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "supports-hyperlinks": "^3.0.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "write-file-atomic": "^5.0.1"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@changesets/get-github-info": "^0.5.2", "@jest/globals": "^29.6.4", "@stylelint/prettier-config": "^3.0.0", "@stylelint/remark-preset": "^4.0.0", "@types/balanced-match": "^1.0.3", "@types/css-tree": "^2.3.1", "@types/debug": "^4.1.8", "@types/file-entry-cache": "^5.0.2", "@types/global-modules": "^2.0.0", "@types/globjoin": "^0.1.0", "@types/imurmurhash": "^0.1.1", "@types/micromatch": "^4.0.2", "@types/normalize-path": "^3.0.0", "@types/postcss-less": "^4.0.2", "@types/postcss-resolve-nested-selector": "^0.1.0", "@types/postcss-safe-parser": "^5.0.1", "@types/style-search": "^0.1.3", "@types/svg-tags": "^1.0.0", "@types/write-file-atomic": "^4.0.0", "benchmark": "^2.1.4", "common-tags": "^1.8.2", "deepmerge": "^4.3.1", "eslint": "^8.48.0", "eslint-config-stylelint": "^20.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.2.3", "husky": "^8.0.3", "jest": "^29.6.4", "jest-preset-stylelint": "^6.2.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^14.0.1", "np": "^8.0.4", "npm-run-all": "^4.1.5", "patch-package": "^8.0.0", "postcss-html": "^1.5.0", "postcss-import": "^15.1.0", "postcss-less": "^6.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.7", "remark-cli": "^11.0.0", "sugarss": "^4.0.1", "typescript": "^5.2.2"}, "engines": {"node": "^14.13.1 || >=16.0.0"}}
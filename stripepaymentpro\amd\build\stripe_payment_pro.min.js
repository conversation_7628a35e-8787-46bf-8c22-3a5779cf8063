define(["exports","core/ajax"],function(e,t){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=o(t);const{call:s}=n.default,r=e=>{const t=new Map;return{getelement(o){const n=`${o}-${e}`;return t.has(n)||t.set(n,document.getElementById(n)),t.get(n)},setelement(e,t){const o=this.getelement(e);o&&(o.innerHTML=t)},toggleelement(e,t){const o=this.getelement(e);o&&(o.style.display=t?"block":"none")},focuselement(e){const t=this.getelement(e);t&&t.focus()},setbutton(e,t,o,n=(t?"0.7":"1")){const s=this.getelement(e);s&&(s.disabled=t,s.textContent=o,s.style.opacity=n,s.style.cursor=t?"not-allowed":"pointer")}}};const a=()=>{console.log("Coupon settings initialized")};var c={stripe_payment_pro:function(e,t,o,n,a,c,i){const l=r(o);if(void 0===window.Stripe)return;const u=(e,t,o)=>{let n;switch(o){case"error":n="red";break;case"success":n="green";break;default:n="blue"}l.setelement(e,`<p style="color: ${n}; font-weight: bold;">${t}</p>`),l.toggleelement(e,!0)},d=e=>{l.setelement(e,""),l.toggleelement(e,!1)};[{id:"apply",event:"click",handler:async e=>{e.preventDefault();const n=l.getelement("coupon"),r=n?.value.trim();if(!r)return u("showmessage",a,"error"),void l.focuselement("coupon");l.setbutton("apply",!0,c);try{const e=await((e,t)=>s([{methodname:"moodle_stripepaymentpro_applycoupon",args:{couponinput:e,instanceid:t}}])[0])(r,o);if(void 0===e?.status)throw new Error("Invalid server response");t=r,l.toggleelement("coupon",!1),l.toggleelement("apply",!1),(e=>{if(e.message?u("showmessage",e.message,"error"===e.uistate?"error":"success"):d("showmessage"),l.toggleelement("enrolbutton","paid"===e.uistate),l.toggleelement("total","paid"===e.uistate),"error"!==e.uistate){if(l.toggleelement("discountsection",e.showsections.discountsection),e.showsections.discountsection&&(e.couponname&&l.setelement("discounttag",e.couponname),e.discountamount&&e.currency&&l.setelement("discountamountdisplay",`-${e.currency} ${e.discountamount}`),e.discountamount&&e.discountvalue)){const t="percentoff"===e.coupontype?`${e.discountvalue}% off`:`${e.currency} ${e.discountvalue} off`;l.setelement("discountnote",t)}if(e.status&&e.currency){const t=l.getelement("totalamount");t&&(t.textContent=`${e.currency} ${parseFloat(e.status).toFixed(2)}`)}}})(e)}catch(e){u("showmessage",e.message||"Coupon validation failed","error"),l.focuselement("coupon")}}},{id:"enrolbutton",event:"click",handler:async()=>{if(l.getelement("enrolbutton")){d("paymentresponse"),l.setbutton("enrolbutton",!0,n);try{const n=await((e,t,o)=>s([{methodname:"moodle_stripepaymentpro_stripe_enrol",args:{userid:e,couponid:t,instanceid:o}}])[0])(e,t,o);n.error?.message?u("paymentresponse",n.error.message,"error"):"success"===n.status&&n.redirecturl?window.location.href=n.redirecturl:u("paymentresponse","Unknown error occurred during payment.","error")}catch(e){u("paymentresponse",e.message,"error")}finally{l.toggleelement("enrolbutton",!1)}}}}].forEach(({id:e,event:t,handler:o})=>{const n=l.getelement(e);n&&n.addEventListener(t,o)})},deactivateCoupon:(e,t)=>s([{methodname:"moodle_stripepaymentpro_deactivate_coupon",args:{courseid:e,couponid:t}}])[0],deactivateAllCoupons:e=>s([{methodname:"moodle_stripepaymentpro_deactivate_all_coupons",args:{courseid:e}}])[0],initCouponSettings:a};e.default=c,e.initCouponSettings=a,Object.defineProperty(e,"__esModule",{value:!0})});

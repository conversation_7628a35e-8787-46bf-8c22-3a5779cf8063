<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Debug script to check course and coupon relationships
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');

// Require admin login
require_login();
require_capability('moodle/site:config', context_system::instance());

$PAGE->set_url('/enrol/stripepaymentpro/debug_courses.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Debug Course-Coupon Relationships');
$PAGE->set_heading('Stripe Payment Pro - Debug Course-Coupon Relationships');

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>Debug: Course-Coupon Relationships</h2>';

// Get all coupons
echo '<h3>1. All Coupons in Database</h3>';
$coupons = $DB->get_records('enrol_stripepro_coupons');
if ($coupons) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped table-sm">';
    echo '<thead><tr><th>Coupon ID</th><th>Coupon Name</th><th>Stripe Product ID</th></tr></thead>';
    echo '<tbody>';
    foreach ($coupons as $coupon) {
        echo '<tr>';
        echo '<td><code>' . htmlspecialchars($coupon->couponid) . '</code></td>';
        echo '<td>' . htmlspecialchars($coupon->coupon_name) . '</td>';
        echo '<td><code>' . htmlspecialchars($coupon->stripe_product_id) . '</code></td>';
        echo '</tr>';
    }
    echo '</tbody></table>';
    echo '</div>';
} else {
    echo '<div class="alert alert-info">No coupons found in database.</div>';
}

// Get all enrollment instances with customtext2
echo '<h3>2. All Enrollment Instances with Stripe Product IDs</h3>';
$enrollments = $DB->get_records_sql(
    "SELECT e.id, e.enrol, e.customtext2, c.id as courseid, c.fullname, c.shortname
     FROM {enrol} e
     JOIN {course} c ON e.courseid = c.id
     WHERE e.customtext2 IS NOT NULL AND e.customtext2 != ''
     ORDER BY e.enrol, c.fullname"
);

if ($enrollments) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped table-sm">';
    echo '<thead><tr><th>Enrollment Method</th><th>Course</th><th>Stripe Product ID</th></tr></thead>';
    echo '<tbody>';
    foreach ($enrollments as $enrollment) {
        echo '<tr>';
        echo '<td><span class="badge badge-' . ($enrollment->enrol == 'stripepaymentpro' ? 'primary' : 'secondary') . '">' . htmlspecialchars($enrollment->enrol) . '</span></td>';
        echo '<td>' . htmlspecialchars($enrollment->fullname) . ' <small>(' . htmlspecialchars($enrollment->shortname) . ')</small></td>';
        echo '<td><code>' . htmlspecialchars($enrollment->customtext2) . '</code></td>';
        echo '</tr>';
    }
    echo '</tbody></table>';
    echo '</div>';
} else {
    echo '<div class="alert alert-warning">No enrollment instances with Stripe Product IDs found.</div>';
}

// Check specific product ID
$specific_product_id = 'prod_SfgkUMADo0hjnr';
echo '<h3>3. Specific Product ID Lookup: ' . htmlspecialchars($specific_product_id) . '</h3>';

$specific_course = $DB->get_record_sql(
    "SELECT e.customtext2, c.id, c.fullname, c.shortname, e.enrol
     FROM {course} c
     JOIN {enrol} e ON c.id = e.courseid
     WHERE e.customtext2 = ?",
    [$specific_product_id]
);

if ($specific_course) {
    echo '<div class="alert alert-success">';
    echo '<h5>✓ Course Found!</h5>';
    echo '<ul>';
    echo '<li><strong>Course Name:</strong> ' . htmlspecialchars($specific_course->fullname) . '</li>';
    echo '<li><strong>Course Short Name:</strong> ' . htmlspecialchars($specific_course->shortname) . '</li>';
    echo '<li><strong>Course ID:</strong> ' . $specific_course->id . '</li>';
    echo '<li><strong>Enrollment Method:</strong> ' . htmlspecialchars($specific_course->enrol) . '</li>';
    echo '<li><strong>Product ID:</strong> ' . htmlspecialchars($specific_course->customtext2) . '</li>';
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="alert alert-danger">';
    echo '<h5>✗ Course Not Found!</h5>';
    echo '<p>No course found with Product ID: <code>' . htmlspecialchars($specific_product_id) . '</code></p>';
    echo '</div>';
}

// Test the coupon controller
echo '<h3>4. Test Coupon Controller</h3>';
try {
    require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/controller/coupon_controller.php');
    $coupon_controller = new \enrol_stripepaymentpro\controller\coupon_controller();
    
    $coupon_data = $coupon_controller->get_coupons_data();
    
    echo '<div class="alert alert-success">✓ Coupon Controller loaded successfully</div>';
    
    if ($coupon_data['has_coupons']) {
        echo '<h5>Coupon Data from Controller:</h5>';
        echo '<div class="table-responsive">';
        echo '<table class="table table-striped table-sm">';
        echo '<thead><tr><th>Product ID</th><th>Course Name (from controller)</th><th>Coupons</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($coupon_data['coupons_by_course'] as $product_id => $coupon_ids) {
            $course_name = isset($coupon_data['courses'][$product_id]) ? 
                $coupon_data['courses'][$product_id]->fullname : 'NOT FOUND';
            
            echo '<tr>';
            echo '<td><code>' . htmlspecialchars($product_id) . '</code></td>';
            echo '<td>' . htmlspecialchars($course_name) . '</td>';
            echo '<td>' . count($coupon_ids) . ' coupon(s)</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
        echo '</div>';
    } else {
        echo '<div class="alert alert-info">No coupons found by controller.</div>';
    }
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">✗ Error loading Coupon Controller: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

echo '<h3>5. Recommendations</h3>';
echo '<div class="alert alert-info">';
echo '<h5>To fix the "Unknown Course" issue:</h5>';
echo '<ol>';
echo '<li>Check if the course with Product ID <code>' . htmlspecialchars($specific_product_id) . '</code> exists in your Moodle installation</li>';
echo '<li>Verify that the course has a Stripe enrollment method configured</li>';
echo '<li>Ensure the <code>customtext2</code> field in the enrollment instance matches the Product ID</li>';
echo '<li>If the course was deleted, you may need to clean up orphaned coupons</li>';
echo '</ol>';
echo '</div>';

echo '<div class="mt-3">';
echo '<a href="coupon_management.php" class="btn btn-primary">Back to Coupon Management</a>';
echo '</div>';

echo '</div>';

echo $OUTPUT->footer();

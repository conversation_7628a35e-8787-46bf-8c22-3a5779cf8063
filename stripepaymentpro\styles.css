.strip-subcription-plan-details{
	max-width: 400px;
	width: 100%;
	margin: auto;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	gap: 0.25rem;
	margin-bottom: 0.75rem;
}
.strip-subcription-plan-details.details-content{
	flex-direction: column;
}
.strip-subcription-plan-details p{
	margin: 0;
	font-weight: 600;
}

button#apply {
        color: #fff;
        border: 0;
        padding: 5px 16px;
        border-radius: 0.5rem;
        font-size: 13px;
    }
    button#payButton,
    button#card-button-zero {
        color: #fff;
        border: 0;
        padding: 5px 32px;
        border-radius: 0.25rem;
        font-size: 13px;
        box-shadow: 0 0.125rem 0.25rem #645cff2e;
        width: 100%;
    }
.displaynone{
        display: none;
    }

.container:has(.table-striped){
	margin: 2rem 0 0 0;
	max-width: 100%;
    padding: 0;
    overflow-x: auto;
}

.table-striped thead th{
	vertical-align: middle;
}

.table-striped .btn-secondary:not(:disabled):not(.disabled):active:focus{
	box-shadow: none;
	outline: none;
}
.table-striped .btn-secondary:focus, .btn-secondary.focus{
	outline: none;
	box-shadow: none;
}

.generate-coupon-section{
	display: none;
}

#page.drawers div[role="main"]:has(.strip-coupon-navigation-section){
	padding: 0 !important; //
}



.strip-coupon-navigation-section {
  display: flex;
  max-width: -moz-fit-content;
  max-width: fit-content;
  width: 100%;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  margin-bottom: 1rem;
}
.strip-coupon-navigation-section button {
  background-color: transparent;
  padding: 0.5rem;
  cursor: pointer;
  border: none;
  outline: none;
  box-shadow: none;
}
.strip-coupon-navigation-section button.active {
  color: #0f6cbf;
  border-bottom: 1px solid #0f6cbf;
}

/* Coupon Management Section Styles */
.all-coupons-section {
  overflow-x: auto;
  width: 100%;
  margin: 1rem 0;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.all-coupons-section .table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

.all-coupons-section table {
  width: 100%;
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.all-coupons-section table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.all-coupons-section table thead th {
  padding: 1rem;
  font-weight: 600;
  text-align: left;
  border: none;
  position: relative;
  vertical-align: middle;
}

.all-coupons-section table thead th:first-child {
  border-top-left-radius: 8px;
}

.all-coupons-section table thead th:last-child {
  border-top-right-radius: 8px;
}

.all-coupons-section table tbody tr {
  transition: background-color 0.2s ease;
}

.all-coupons-section table tbody tr:hover {
  background-color: #f8f9fa;
}

.all-coupons-section table tbody tr:nth-child(even) {
  background-color: #ffffff;
}

.all-coupons-section table tbody tr:nth-child(odd) {
  background-color: #fafbfc;
}

.all-coupons-section table tbody td {
  padding: 1rem;
  border: none;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  min-height: 60px;
}

.all-coupons-section table tbody tr:last-child td {
  border-bottom: none;
}

/* Course Name Column */
.all-coupons-section table tbody td:first-child {
  font-weight: 600;
  color: #495057;
  max-width: 200px;
  word-wrap: break-word;
}

/* Coupon List Column */
.all-coupons-section table tbody td.coupon-list {
  display: block;
  max-width: 300px;
}

.all-coupons-section .coupon-name {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  margin: 0.25rem;
  border-radius: 6px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #e1e5e9;
  font-size: 0.875rem;
  min-width: 120px;
  transition: all 0.2s ease;
}

.all-coupons-section .coupon-name:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.all-coupons-section .coupon-name strong {
  color: #2c3e50;
  margin-right: 0.5rem;
}

.all-coupons-section .coupon-name small {
  display: block;
  margin-top: 0.25rem;
  color: #6c757d;
  font-size: 0.75rem;
}

.all-coupons-section .coupon-name .deactivate-coupon {
  background: transparent;
  border: none;
  padding: 0.25rem;
  margin-left: 0.5rem;
  border-radius: 4px;
  color: #dc3545;
  transition: all 0.2s ease;
  cursor: pointer;
}

.all-coupons-section .coupon-name .deactivate-coupon:hover {
  background-color: #dc3545;
  color: white;
  transform: scale(1.1);
}

/* Coupon ID Column */
.all-coupons-section .coupon-id-list {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  color: #6c757d;
  max-width: 150px;
  word-break: break-all;
}

.all-coupons-section .coupon-id-list div {
  padding: 0.25rem 0.5rem;
  margin: 0.25rem 0;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* Actions Column */
.all-coupons-section .deactivate-all-coupons {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.all-coupons-section .deactivate-all-coupons:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3);
  background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
}

.all-coupons-section .deactivate-all-coupons i {
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-coupons-section table {
    font-size: 0.875rem;
  }

  .all-coupons-section table thead th,
  .all-coupons-section table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .all-coupons-section .coupon-name {
    flex-direction: column;
    align-items: flex-start;
    min-width: 100px;
  }

  .all-coupons-section .coupon-name .deactivate-coupon {
    margin-left: 0;
    margin-top: 0.25rem;
    align-self: flex-end;
  }
}

@media (max-width: 576px) {
  .all-coupons-section {
    margin: 0.5rem -15px;
    border-radius: 0;
  }

  .all-coupons-section .table-responsive {
    border-radius: 0;
  }

  .all-coupons-section table thead th:first-child,
  .all-coupons-section table thead th:last-child {
    border-radius: 0;
  }
}
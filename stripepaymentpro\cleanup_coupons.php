<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Cleanup orphaned coupons
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/classes/controller/coupon_controller.php');

use enrol_stripepaymentpro\controller\coupon_controller;

// Require admin login
require_login();
require_capability('moodle/site:config', context_system::instance());

$PAGE->set_url('/enrol/stripepaymentpro/cleanup_coupons.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Cleanup Orphaned Coupons');
$PAGE->set_heading('Stripe Payment Pro - Cleanup Orphaned Coupons');

$action = optional_param('action', '', PARAM_ALPHA);
$confirm = optional_param('confirm', 0, PARAM_INT);

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>Cleanup Orphaned Coupons</h2>';

$coupon_controller = new coupon_controller();

if ($action === 'cleanup' && $confirm) {
    // Perform cleanup
    echo '<div class="alert alert-info">Cleaning up orphaned coupons...</div>';
    
    try {
        $cleaned_count = $coupon_controller->cleanup_orphaned_coupons();
        
        if ($cleaned_count > 0) {
            echo '<div class="alert alert-success">';
            echo '<h5>✓ Cleanup Completed!</h5>';
            echo '<p>Successfully cleaned up ' . $cleaned_count . ' orphaned coupon(s).</p>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-info">No orphaned coupons found to clean up.</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h5>✗ Cleanup Failed!</h5>';
        echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    
} else {
    // Show orphaned coupons and cleanup options
    echo '<p>This tool will help you identify and clean up coupons that reference non-existent courses.</p>';
    
    try {
        $orphaned_coupons = $coupon_controller->get_orphaned_coupons();
        
        if (!empty($orphaned_coupons)) {
            echo '<div class="alert alert-warning">';
            echo '<h5>⚠ Orphaned Coupons Found</h5>';
            echo '<p>The following coupons reference courses that no longer exist:</p>';
            echo '</div>';
            
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>Coupon Name</th>';
            echo '<th>Coupon ID</th>';
            echo '<th>Product ID</th>';
            echo '<th>Created</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($orphaned_coupons as $coupon) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($coupon->coupon_name) . '</td>';
                echo '<td><code>' . htmlspecialchars($coupon->couponid) . '</code></td>';
                echo '<td><code>' . htmlspecialchars($coupon->stripe_product_id) . '</code></td>';
                echo '<td>' . ($coupon->timecreated ? userdate($coupon->timecreated) : 'Unknown') . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            echo '</div>';
            
            echo '<div class="alert alert-danger">';
            echo '<h5>⚠ Warning</h5>';
            echo '<p>Cleaning up these coupons will:</p>';
            echo '<ul>';
            echo '<li>Delete the coupons from your Stripe account</li>';
            echo '<li>Remove the coupon records from your Moodle database</li>';
            echo '<li>This action cannot be undone</li>';
            echo '</ul>';
            echo '</div>';
            
            echo '<form method="post" action="cleanup_coupons.php">';
            echo '<input type="hidden" name="action" value="cleanup">';
            echo '<input type="hidden" name="confirm" value="1">';
            echo '<div class="form-check mb-3">';
            echo '<input class="form-check-input" type="checkbox" id="confirm_cleanup" required>';
            echo '<label class="form-check-label" for="confirm_cleanup">';
            echo 'I understand that this will permanently delete ' . count($orphaned_coupons) . ' orphaned coupon(s)';
            echo '</label>';
            echo '</div>';
            echo '<button type="submit" class="btn btn-danger">Clean Up Orphaned Coupons</button>';
            echo '</form>';
            
        } else {
            echo '<div class="alert alert-success">';
            echo '<h5>✓ No Orphaned Coupons Found</h5>';
            echo '<p>All coupons are properly linked to existing courses.</p>';
            echo '</div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h5>✗ Error Checking for Orphaned Coupons</h5>';
        echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
}

echo '<h3>Alternative Solutions</h3>';
echo '<div class="alert alert-info">';
echo '<h5>If you prefer not to delete the coupons:</h5>';
echo '<ol>';
echo '<li><strong>Recreate the missing course:</strong> Create a new course and configure it with the same Stripe Product ID</li>';
echo '<li><strong>Update the Product ID:</strong> Change the coupon\'s product ID to match an existing course</li>';
echo '<li><strong>Make it global:</strong> Change the product ID to "0" to make it applicable to all courses</li>';
echo '</ol>';
echo '</div>';

echo '<div class="mt-3">';
echo '<a href="debug_courses.php" class="btn btn-secondary">Debug Course Relationships</a> ';
echo '<a href="coupon_management.php" class="btn btn-primary">Back to Coupon Management</a>';
echo '</div>';

echo '</div>';

echo $OUTPUT->footer();
